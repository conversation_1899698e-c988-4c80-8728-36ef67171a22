#!/usr/bin/env python3
"""
Deployment script for E3 Automation Tools Repository

This script helps deploy the e3-automation-tools repository to GitHub.
"""

import os
import subprocess
import sys
from pathlib import Path

def run_command(command, cwd=None):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(command, shell=True, cwd=cwd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ Command failed: {command}")
            print(f"Error: {result.stderr}")
            return False
        return True
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return False

def main():
    """Main deployment function"""
    print("🚀 E3 Automation Tools Repository Deployment")
    print("=" * 50)

    # Get the script directory and look for preview directory
    script_dir = Path(__file__).parent
    preview_dir = script_dir / "e3-automation-preview"

    print(f"🔍 Looking for preview directory at: {preview_dir.absolute()}")

    if not preview_dir.exists():
        print("❌ e3-automation-preview directory not found!")
        print(f"Script directory: {script_dir.absolute()}")
        print("Make sure the e3-automation-preview directory exists.")
        sys.exit(1)
    
    print("✅ Found e3-automation-preview directory")

    # Get repository URL
    repo_url = "https://github.com/nahallac/e3-automation-tools.git"
    print(f"📡 Target repository: {repo_url}")

    # Change to preview directory
    os.chdir(preview_dir)
    print(f"📁 Changed to directory: {preview_dir.absolute()}")

    # Check if this is already a git repository
    if Path(".git").exists():
        print("⚠️  Git repository already exists. Cleaning up...")
        if not run_command("git status"):
            print("🔄 Reinitializing git repository...")
            run_command("rm -rf .git" if os.name != 'nt' else "rmdir /s /q .git")
        else:
            print("🔄 Using existing git repository...")
    
    # Initialize git repository
    print("\n🔧 Setting up Git repository...")
    
    commands = [
        "git init",
        "git add .",
        'git commit -m "Initial commit: E3 automation tools"',
        "git branch -M main",
        f"git remote add origin {repo_url}"
    ]
    
    for command in commands:
        print(f"⚡ Running: {command}")
        if not run_command(command):
            print(f"❌ Failed to execute: {command}")
            sys.exit(1)
    
    print("\n🚀 Ready to push to GitHub!")
    print("⚠️  IMPORTANT: Make sure you've created the GitHub repository first!")
    print(f"   Repository URL: {repo_url}")
    print()
    
    # Ask for confirmation
    response = input("Push to GitHub now? (y/N): ").strip().lower()
    if response == 'y':
        print("📤 Pushing to GitHub...")
        if run_command("git push -u origin main"):
            print("✅ Successfully deployed to GitHub!")
            print()
            print("🎉 Next steps:")
            print("1. Set up personal access tokens")
            print("2. Configure repository secrets")
            print("3. Add sync trigger to main repository")
        else:
            print("❌ Failed to push to GitHub")
            print("Make sure:")
            print("- The repository exists on GitHub")
            print("- You have push permissions")
            print("- Your Git credentials are configured")
    else:
        print("⏸️  Deployment prepared but not pushed.")
        print("To push manually later, run:")
        print("   git push -u origin main")

if __name__ == "__main__":
    main()
